Enums.Player = {
    Manager = {
        Request = "fowlmas:onelife:player:manager:request",
        Init = "fowlmas:onelife:player:manager:init",
        Add = "fowlmas:onelife:player:manager:add",
        Added = "fowlmas:onelife:player:manager:added",
        Remove = "fowlmas:onelife:player:manager:remove",
        Removed = "fowlmas:onelife:player:manager:removed",
        Set = "fowlmas:onelife:player:manager:set",
        SetAll = "fowlmas:onelife:player:manager:set_all"
    },
    Events = {
        updateZonesAndBlips = "fowlmas:onelife:player:update_zones&blips",
        LoadPlayerData = "fowlmas:onelife:player:load_player_data",
        ReceivePlayerData = "fowlmas:onelife:player:receive_player_data",
        PlayerLoaded = "fowlmas:onelife:player:player_loaded",
        KickPlayer = "fowlmas:onelife:player:kick_player",
        onDeath = "fowlmas:onelife:player:on_death",
        onRevive = "fowlmas:onelife:player:on_revive",
        LoadSkin = "fowlmas:onelife:player:load:skin",
        SetWaypoint = "fowlmas:onelife:player:set:waypoint"
    };
};