/*=================================================================*\
/* By: 			|	Nevera Development  							|
/* FiveM: 		|	https://forum.cfx.re/u/neveradevelopment		|
/* Discord: 	|	https://discord.gg/NeveraDev/tw28AqrgWU  		|
/*=================================================================*/
/* If you have any problems you can contact us via discord. <3     */


var staff_team = [

	{
		"name":"Nevera Owner",
		"image":"https://forum.cfx.re/user_avatar/forum.cfx.re/neveradevelopment/144/4447132_2.png",
		"rank":"Owner"
	},
	{
		"name":"Nevera Admin",
		"image":"https://forum.cfx.re/user_avatar/forum.cfx.re/neveradevelopment/144/4447132_2.png",
		"rank":"Admin"
	}

]


// Staff Settings
const showStaffTeam = false
const showPlayersList = false


// orange
// red
// blue
// green
// pink
// purple
const theme = "blue"

// ==== WINTER UPDATE !! ==== \\
const enableWinterUpdate = false
// ==== WINTER UPDATE !! ==== \\


// Text settings
const name = "<strong>IMPE</strong>RIAL"
const underName = "ROLE<b>PLAY</b>"
const desc = "FiveM Roleplay server featuring an extensive collection of custom scripts, maps, vehicles, unique weapons, and much more."


// Social media
const discord = "https://discord.gg/"  // If = "" then icon will not show up on screen
const instagram = "**"	// https://example.com
const youtube = "**" 		// https://example.com
const twitter = "" 		// https://example.com
const tiktok = "**"  		// https://example.com
const facebook = ""		// https://example.com
const twitch = "**" 		// https://example.com
const github = "" 		// https://example.com



// Player List
const serverCode = "******" //Your server CODE from fivem. (cfx.re/join/abcdef) this ABCDEF is your code, paste it in serverCode. 
const playerProfileImage = "https://forum.cfx.re/user_avatar/forum.cfx.re/neveradevelopment/144/4447132_2.png"


// Video Settings
const videoBlur = 0
var videoOpacity = 0.3


// Example link: https://www.youtube.com/watch?v=abcdefgh
const youtubeVideo = ""
const showYoutubeVideo = false

// Local Video
const enableLocalVideo = false

// Local audio
const localAudio = false



// HELP //

//-- YOUTBE
//-- LOCAL AUDIO
// if localAudio is true, then loading will load "audio.mp3" file and play it except youtube audio.
// if localAudio is false, then loading will load youtube audio.

//-- LOCAL VIDEO
// if enableLocalVideo is true, then loading will load "video.webm" file and play it except youtube video.
// If localVideo is enabled, showYoutubeVideo is automatically disabled.
// You can only import a video from either YouTube or local. Local video taking priority.