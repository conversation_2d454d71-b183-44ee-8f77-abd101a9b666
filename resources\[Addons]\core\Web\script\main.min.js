// @ts-nocheck
let audio=new Audio;var request=new XMLHttpRequest,count=0;function Main(){return{DiscordGuildId:"1311520991175901194",DiscordInviteLink:"https://discord.gg/xznMy724v9",memberCount:0,musicAutoplay:!0,musicVolume:.01,buttons:[{label:"Home",selected:!0},{label:"News",selected:!1},{label:"Team",selected:!1}],musicList:[{label:"Won’t Forget You",author:"Shouse",src:"audio/wont_forget_you.mp3"},{label:"Dive Too Deep",author:"<PERSON>z feat. Revel Day",src:"audio/dive_too_deep.mp3"},{label:"Habits (Stay High)",author:"Tove <PERSON>",src:"audio/habits.mp3"}],team:[{discord:"Linc0nnu",role:"Developer",img:"img/member1.png"},{discord:"<PERSON><PERSON> <PERSON>",role:"<PERSON>ondateur",img:"img/member1.png"},{discord:"<PERSON><PERSON>",role:"Co-Fondateur",img:"img/member.png"},{discord:"DonovaN7",role:"Co-Fondateur",img:"img/member.png"}],isMusicPlaying:!1,musicOpen:!1,currentSong:0,listen(){this.musicAutoplay&&setTimeout((()=>{this.play()}),100),request.open("GET","https://discordapp.com/api/guilds/"+this.DiscordGuildId+"/widget.json",!0),request.onload=function(){if(request.status>=200&&request.status<400){var e=JSON.parse(request.responseText);count=e.presence_count}},request.onerror=function(){},request.send(),setTimeout((()=>{this.memberCount=count}),1e3)},selectBtn(e){return this.buttons.forEach((function(e){e.selected=!1})),!0},play(){audio.src=this.musicList[this.currentSong].src,audio.load(),audio.play(),audio.volume=this.musicVolume,this.isMusicPlaying=!0},pause(){audio.pause(),this.isMusicPlaying=!1},next(){this.isMusicPlaying&&audio.pause(),this.currentSong<this.musicList.length-1?this.currentSong++:this.currentSong=0,audio.src=this.musicList[this.currentSong].src,audio.load(),audio.play(),this.isMusicPlaying=!0},prev(){this.isMusicPlaying&&audio.pause(),0!=this.currentSong?this.currentSong=this.currentSong-1:this.currentSong=this.musicList.length-1,audio.src=this.musicList[this.currentSong].src,audio.load(),audio.play(),this.isMusicPlaying=!0}}}