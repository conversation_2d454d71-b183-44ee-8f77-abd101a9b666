RegisterNetEvent("iZeyy:factionMenu:NotifyPolice", function(plate, modelName, coords)
    local blip = AddBlipForCoord(coords)
	SetBlipSprite(blip, 161)
	SetBlipScale(blip, 1.2)
    SetBlipColour(blip, 0)
    BeginTextCommandSetBlipName("STRING")
	AddTextComponentString('[~r~Alerte~s~] Vol de véhicule')
	EndTextCommandSetBlipName(blip)
    SetTimeout(80000, function()
        RemoveBlip(blip)
    end)
    ESX.ShowNotification("Vol de véhicule en cours\nPlaque: "..plate.."\nModele: "..modelName)
end)