/*=================================================================*\
/* By: 			|	Nevera Development  							|
/* FiveM: 		|	https://forum.cfx.re/u/neveradevelopment		|
/* Discord: 	|	https://discord.gg/NeveraDev/tw28AqrgWU  		|
/*=================================================================*/
/* If you have any problems you can contact us via discord. <3     */

@import 'https://fonts.googleapis.com/css2?family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap';
*{margin:0;padding:0;border:0;outline:0;text-decoration:none;line-height:inherit;vertical-align:inherit;}h1,h2,h3,h4,h5,h6{font-weight:normal;}ul{list-style:none;}
*{font-family: 'Mulish', sans-serif;}

body {
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

img.logo {
  width: 13vw;
}

.social {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1vw;
  margin-top: 1.5vw;
}

.social li a {
  border-radius: 0.1vw;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 2.25vw;
  height: 2.25vw;
  color: #fff;
  font-size: 1vw;
  color: #b1b1b1de;
  box-shadow: 0px 0px 0px 0.05vw rgba(var(--main), 1) inset;
  color: rgba(var(--main), 1);
  opacity: 0.7;
  transition: 0.3s;
}

.social li a:after {}

.social li a span {
  display: none;
}

.social li a:hover {
  background-color: rgba(var(--main), 1);
  color: #111;
  opacity: 1;
  transform: scale(1.12);
  box-shadow: 0px 0px 0px 0.05vw rgba(var(--main), 1) inset, 0px 0px 1vw #000;

}

.center h1 strong {
  font-weight: 900;
  text-shadow: 0px 0px 2vw rgba(var(--main), 0.5);
  color: rgba(var(--main), 1);
  /*font-family: 'snow', sans-serif;*/
  /*font-weight: 100;*/
}

.center h1 {
  letter-spacing: 0.25vw;
  font-size: 5vw;
  font-weight: 900;
  font-style: italic;
  color: #fff;
  text-shadow: 0px 0px 2vw #ffffff50;
  /*font-family: 'snow', sans-serif;*/
  /*font-weight: 100;*/
}

.center p {
  color: #fff;
  font-weight: 800;
  font-size: 2vw;
  /*text-transform: uppercase;*/
  font-style: italic;
  letter-spacing: 0.2vw;
  margin-top: -1vw;
  text-shadow: 0px 0px 2vw #ffffff50;
}

.center p b {
  font-weight: 900;
  text-shadow: 0px 0px 2vw rgba(var(--main), 0.5);
  color: rgba(var(--main), 1);
}

.center span {
  width: 28vw;
  color: #fff;
  text-align: center;
  margin-top: 0.5vw;
  font-size: 0.75vw;
  color: #dbdbdb;
}

.loading-bar {
  position: fixed;
  bottom: 0vw;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-bar p {
  font-size: 1vw;
  color: #fff;
  margin-bottom: 0.6vw;

  font-size: 0.75vw;
  color: #dbdbdb;
}

.loading-bar .bar {
  margin-bottom: 0.4vw;
  width: 30vw;
  height: 0.3vw;
  border-radius: 100vw;
  background-color: #353535a6;
}

.loading-bar .bar .line {
  height: 100%;
  width: 0%;
  border-radius: 100vw;
  background-color: rgba(var(--main), 1);
  box-shadow: 0px 0px 2vw rgba(var(--main), 0.631);
  transition: 1s;
}

.panel {
  position: fixed;
  width: 20vw;
  border-radius: 0.2vw;
  transition: 0.3s;
  color: #fff;
}

.panel h3 {
  font-size: 1vw;
}

.panel.left {
  position: fixed;
  top: 50%;
  left: 0%;
  margin: 0px 3vw;
  transform: translate(0%, -50%);
}

.panel.right {
  position: fixed;
  top: 50%;
  right: 0%;
  margin: 0px 3vw;
  transform: translate(0%, -50%);
}

.pfp {
  width: 1.5vw;
  height: 1.5vw;
  border-radius: 0.1vw;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 0.3vw;
  margin-top: 0.4vw;
  height: 25vw;
  overflow: auto;
}

::-webkit-scrollbar {
  width: 0px;
}

.staff .info {
  display: flex;
  align-items: center;
  gap: 0.5vw;
  font-weight: 700;
}

.staff {
  background-color: #ffffff0d;
  padding: 0.2vw 0.3vw;
  border-radius: 0.1vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.7vw;
  text-shadow: 0px 0px 0.5vw #000;
  box-shadow: 0.05vw 0px #ff960000 inset, 0px 0px 0px 0.05vw #ffffff10 inset;
  transition: 0.2s;
  background-color: rgba(var(--main), 0.08);
}

.staff .status {
  transition: 0.2s;
  color: rgba(var(--main), 1);
  font-weight: 700;
}

iframe,
video {
  position: absolute;
  width: 100vw;
  height: 100vh;
  z-index: -1;
}

.mini-buttons {
  position: absolute;
  left: 0;
  bottom: 0;
  margin: 1vw;
  color: #fff;
  display: flex;
  gap: 0.5vw;
  display: flex;
  align-items: center;
}

.mini-buttons button {
  border-radius: 0.1vw;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 2.25vw;
  height: 2.25vw;
  color: #fff;
  font-size: 1vw;
  color: #b1b1b1de;
  box-shadow: 0px 0px 0px 0.05vw rgba(var(--main), 1) inset;
  color: rgba(var(--main), 1);
  opacity: 0.7;
  transition: 0.3s;
  background-color: transparent;
}

.mini-buttons button:hover,
.mini-buttons button.act {
  background-color: rgba(var(--main), 1);
  color: #111;
  opacity: 1;
  transform: scale(1.12);
  box-shadow: 0px 0px 0px 0.05vw rgba(var(--main), 1) inset, 0px 0px 1vw #000;
}

.volume-slider {
  -webkit-appearance: none;
  width: 10vw;
  height: 0.1vw;
  background: rgba(var(--main), 1);
  border-radius: 0.1vw;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 1vw;
  height: 1vw;
  /*background: rgba(var(--main), 1);*/
  background-color: #000;
  box-shadow: 0px 0px 0vw 0.1vw rgba(var(--main), 0.5) inset;
  border-radius: 0.1vw;
  cursor: pointer;
  transition: 0.2s;
}

.volume-slider::-webkit-slider-thumb:hover {
  background: rgba(var(--main), 1);
  transform: rotate(45deg);
  box-shadow: 0px 0px 0vw 0.1vw rgba(var(--main), 0.0) inset, 0px 0px 2vw rgba(var(--main), 0.5);
}

.volume-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.3s ease;
}

.inpt {
  display: flex;
  align-items: center;
}

.inpt span {
  margin-left: 0.5vw;
  font-size: 0.6vw;
  opacity: 0.4;
  font-weight: 200;
}

#particles-js {
  background-color: transparent;
  user-select: none;
  pointer-events: none;
  z-index: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  opacity: 0;
}

.winter {
  width: 100vw;
  height: 100vh;
  background: #00ff3c08;
  position: absolute;
  display: none;
  top: 0;
}