local InteractMenuGouv = {}
local gouvInteractionsLoaded = false

-- Fonction pour vérifier si le joueur est du gouvernement
local function isPlayerGouv()
    return Client.Player and Client.Player.job and Client.Player.job.name == "gouv"
end

-- Fonction pour vérifier si le joueur ciblé lève les mains
local function isPlayerHandsUp(targetPed)
    return IsEntityPlayingAnim(targetPed, 'random@mugging3', 'handsup_standing_base', 3)
end

-- Fonction pour vérifier si le joueur ciblé est menotté
local function isPlayerHandcuffed(targetPed)
    return IsEntityPlayingAnim(targetPed, 'mp_arresting', 'idle', 3)
end

-- Fonction pour charger les interactions gouvernement
local function loadGouvInteractions()
    if gouvInteractionsLoaded then
        return
    end
    
    gouvInteractionsLoaded = true

    CreateThread(function()
        while Client.Player == nil do
            Wait(100)
        end

        -- Bouton principal Gouvernement (visible seulement pour le gouvernement)
        InteractMenuGouv.main = Game.InteractContext:AddButton("ped_menu", "Gouvernement", nil, function(onSelected, Entity)
            if (onSelected) then end
        end, function(Entity)
            -- Vérifier si le joueur est du gouvernement et que l'entité n'est pas lui-même
            return isPlayerGouv() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Menotter
        InteractMenuGouv.handcuff = Game.InteractContext:AddSubMenu(InteractMenuGouv.main, "Menotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if not isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:gouv:interact:handcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne est déjà menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerGouv() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and not isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Démenotter
        InteractMenuGouv.unhandcuff = Game.InteractContext:AddSubMenu(InteractMenuGouv.main, "Démenotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:gouv:interact:unhandcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne n'est pas menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerGouv() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Escorter
        InteractMenuGouv.escort = Game.InteractContext:AddSubMenu(InteractMenuGouv.main, "Escorter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:gouv:interact:escort", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerGouv() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Fouiller
        InteractMenuGouv.frisk = Game.InteractContext:AddSubMenu(InteractMenuGouv.main, "Fouiller", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:gouv:interact:frisk", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerGouv() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Voir la carte d'identité
        InteractMenuGouv.showId = Game.InteractContext:AddSubMenu(InteractMenuGouv.main, "Carte d'identité", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:gouv:interact:showId", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerGouv() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Voir les factures
        InteractMenuGouv.showBills = Game.InteractContext:AddSubMenu(InteractMenuGouv.main, "Voir factures", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:gouv:interact:showBills", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerGouv() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)
    end)
end

-- Charger les interactions quand le joueur est connecté
AddEventHandler("fowlmas:onelife:player:receive_player_data", function()
    loadGouvInteractions()
end)

-- Fallback au cas où l'événement ne se déclenche pas
CreateThread(function()
    Wait(10000) -- Attendre 10 secondes
    if not gouvInteractionsLoaded then
        loadGouvInteractions()
    end
end)
