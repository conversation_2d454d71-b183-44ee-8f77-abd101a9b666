local InteractMenuFib = {}
local fibInteractionsLoaded = false

-- Fonction pour vérifier si le joueur est du FIB
local function isPlayerFib()
    return Client.Player and Client.Player.job and Client.Player.job.name == "fib"
end

-- Fonction pour vérifier si le joueur ciblé lève les mains
local function isPlayerHandsUp(targetPed)
    return IsEntityPlayingAnim(targetPed, 'random@mugging3', 'handsup_standing_base', 3)
end

-- Fonction pour vérifier si le joueur ciblé est menotté
local function isPlayerHandcuffed(targetPed)
    return IsEntityPlayingAnim(targetPed, 'mp_arresting', 'idle', 3)
end

-- Fonction pour charger les interactions FIB
local function loadFibInteractions()
    if fibInteractionsLoaded then
        return
    end
    
    fibInteractionsLoaded = true

    CreateThread(function()
        while Client.Player == nil do
            Wait(100)
        end

        -- Bouton principal FIB (visible seulement pour le FIB)
        InteractMenuFib.main = Game.InteractContext:AddButton("ped_menu", "FIB", nil, function(onSelected, Entity)
            if (onSelected) then end
        end, function(Entity)
            -- Vérifier si le joueur est du FIB et que l'entité n'est pas lui-même
            return isPlayerFib() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Menotter (visible seulement si la personne n'est pas menottée)
        InteractMenuFib.handcuff = Game.InteractContext:AddSubMenu(InteractMenuFib.main, "Menotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if not isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:fib:interact:handcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne est déjà menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerFib() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and not isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Démenotter (visible seulement si la personne est menottée)
        InteractMenuFib.unhandcuff = Game.InteractContext:AddSubMenu(InteractMenuFib.main, "Démenotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:fib:interact:unhandcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne n'est pas menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerFib() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Escorter
        InteractMenuFib.escort = Game.InteractContext:AddSubMenu(InteractMenuFib.main, "Escorter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:fib:interact:escort", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerFib() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Fouiller
        InteractMenuFib.frisk = Game.InteractContext:AddSubMenu(InteractMenuFib.main, "Fouiller", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:fib:interact:frisk", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerFib() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Voir la carte d'identité
        InteractMenuFib.showId = Game.InteractContext:AddSubMenu(InteractMenuFib.main, "Carte d'identité", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:fib:interact:showId", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerFib() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Voir les factures
        InteractMenuFib.showBills = Game.InteractContext:AddSubMenu(InteractMenuFib.main, "Voir factures", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:fib:interact:showBills", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerFib() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)
    end)
end

-- Charger les interactions quand le joueur est connecté
AddEventHandler("fowlmas:onelife:player:receive_player_data", function()
    loadFibInteractions()
end)

-- Fallback au cas où l'événement ne se déclenche pas
CreateThread(function()
    Wait(10000) -- Attendre 10 secondes
    if not fibInteractionsLoaded then
        loadFibInteractions()
    end
end)
