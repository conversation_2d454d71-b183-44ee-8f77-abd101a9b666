local InteractMenuBcso = {}
local bcsoInteractionsLoaded = false

-- Fonction pour vérifier si le joueur est du BCSO
local function isPlayerBcso()
    return Client.Player and Client.Player.job and Client.Player.job.name == "bcso"
end

-- Fonction pour vérifier si le joueur ciblé lève les mains
local function isPlayerHandsUp(targetPed)
    return IsEntityPlayingAnim(targetPed, 'random@mugging3', 'handsup_standing_base', 3)
end

-- Fonction pour vérifier si le joueur ciblé est menotté
local function isPlayerHandcuffed(targetPed)
    return IsEntityPlayingAnim(targetPed, 'mp_arresting', 'idle', 3)
end

-- Fonction pour charger les interactions BCSO
local function loadBcsoInteractions()
    if bcsoInteractionsLoaded then
        return
    end
    
    bcsoInteractionsLoaded = true

    CreateThread(function()
        while Client.Player == nil do
            Wait(100)
        end

        -- Bouton principal BCSO (visible seulement pour le BCSO)
        InteractMenuBcso.main = Game.InteractContext:AddButton("ped_menu", "BCSO", nil, function(onSelected, Entity)
            if (onSelected) then end
        end, function(Entity)
            -- Vérifier si le joueur est du BCSO et que l'entité n'est pas lui-même
            return isPlayerBcso() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Menotter
        InteractMenuBcso.handcuff = Game.InteractContext:AddSubMenu(InteractMenuBcso.main, "Menotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if not isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:bcso:interact:handcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne est déjà menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerBcso() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and not isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Démenotter
        InteractMenuBcso.unhandcuff = Game.InteractContext:AddSubMenu(InteractMenuBcso.main, "Démenotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:bcso:interact:unhandcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne n'est pas menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerBcso() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Escorter
        InteractMenuBcso.escort = Game.InteractContext:AddSubMenu(InteractMenuBcso.main, "Escorter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:bcso:interact:escort", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerBcso() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Fouiller
        InteractMenuBcso.frisk = Game.InteractContext:AddSubMenu(InteractMenuBcso.main, "Fouiller", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:bcso:interact:frisk", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerBcso() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Voir la carte d'identité
        InteractMenuBcso.showId = Game.InteractContext:AddSubMenu(InteractMenuBcso.main, "Carte d'identité", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:bcso:interact:showId", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerBcso() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Voir les factures
        InteractMenuBcso.showBills = Game.InteractContext:AddSubMenu(InteractMenuBcso.main, "Voir factures", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:bcso:interact:showBills", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerBcso() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)
    end)
end

-- Charger les interactions quand le joueur est connecté
AddEventHandler("fowlmas:onelife:player:receive_player_data", function()
    loadBcsoInteractions()
end)

-- Fallback au cas où l'événement ne se déclenche pas
CreateThread(function()
    Wait(10000) -- Attendre 10 secondes
    if not bcsoInteractionsLoaded then
        loadBcsoInteractions()
    end
end)
